# Environnement Docker Riashop Local

Environnement de développement Docker basé sur l'architecture V3 de production Riashop, avec setup automatique par client via SVN.

## 🏗️ Architecture

Basé sur l'architecture V3 de production :
- **1 conteneur principal** avec Apache, PHP 5.6, et tous les services requis
- **Services dédiés** : MariaDB, CouchDB, Memcache, Beanstalk
- **Workers Python** pour le traitement asynchrone
- **Structure de dossiers conforme** à la production

### Structure des dossiers

```
www/
├── engine/                    # Moteur Riashop (accès local uniquement)
│   ├── htdocs/api/           # API accessible via api.localhost
│   ├── htdocs/sync/          # Sync accessible via sync.localhost
│   ├── include/              # Librairies communes
│   ├── crontabs/             # Scripts cron
│   └── .env                  # Configuration environnement
├── sites/
│   ├── riashop/              # Backoffice Riashop (localhost)
│   └── [nom_client]/         # Frontend client (front.localhost) (optionel)
└── media/                    # Médias et ressources statiques
```

## 🚀 Installation rapide

### Prérequis

- Docker et Docker Compose installés
- SVN installé
- Accès aux dépôts SVN Riashop
- Ports disponibles : 80, 443, 3306, 5984, 8080, 8081, 11211, 11300, 1025, 8025, 2080

### 🐘 Configuration PHP 5.6

✅ **Compatibilité garantie**: Le conteneur utilise **PHP 5.6** pour une compatibilité complète avec le code legacy Riashop.

**Extensions incluses** :
- `mysql` - Fonctions MySQL legacy (`mysql_connect()`, `mysql_query()`, etc.)
- `mysqli` - Interface MySQL améliorée
- `pdo_mysql` - Interface PDO pour MySQL
- `mcrypt` - Chiffrement/déchiffrement
- `gettext` - Internationalisation
- `memcached` - Cache distribué
- `xml`, `zip` - Manipulation de fichiers

**Avantages** :
- ✅ Aucune erreur de compatibilité MySQL
- ✅ Support complet des fonctions legacy
- ✅ Environnement identique à la production historique

### Installation automatique

```bash
# Installation complète avec un client
./setup.sh \
   --engine "https://svn.riashop.fr/svn/riashop-engine/branches/localhost" \
   --backoffice "https://svn.riashop.fr/svn/riashop-admin-transition/branches/localhost" \
   --frontend "https://svn.example.com/clients/myclient" \
   --client "myclient"

# Ou avec Make
make setup \
  ENGINE_SVN_URL="https://svn.riashop.fr/svn/riashop-engine/branches/localhost" \
  BACKOFFICE_SVN_URL="https://svn.riashop.fr/svn/riashop-admin-transition/branches/localhost" \
  FRONTEND_SVN_URL="https://svn.example.com/clients/myclient" \
  CLIENT_NAME="myclient"
```

## 🗄️ Gestion des bases de données

### Import automatique de base de données

Le système supporte l'import automatique de dumps SQL lors de la création de clients :

#### Mode interactif (recommandé)
```bash
# L'assistant vous demandera le fichier SQL à importer
./client-manager.sh add
```

#### Mode traditionnel
```bash
# Ajouter un client avec import de base de données
./client-manager.sh add Sardeco --tnt-id 36 \
  --sql-file "/path/to/sardeco_database.sql" \
  --frontend-svn "https://svn.riashop.fr/svn/sardeco/branches/extranet"

# Ou avec un fichier dans le répertoire databases/
./client-manager.sh add MyClient --tnt-id 123 \
  --sql-file "databases/MyClient.sql"
```

### Répertoire databases/

Placez vos dumps SQL dans le répertoire `databases/` :

```
databases/
├── README.md           # Documentation
├── .gitkeep           # Préserve le répertoire
└── [CLIENT_NAME].sql  # Vos dumps SQL (exclus de git)
```

**⚠️ Important** : Les fichiers SQL sont automatiquement exclus de git pour éviter de committer des données sensibles.

### Fonctionnalités de l'import

- ✅ **Validation automatique** du fichier SQL avant import
- ✅ **Gestion des gros fichiers** (avec avertissement et suivi de progression)
- ✅ **Filtrage intelligent** des instructions problématiques (`DROP DATABASE`, `CREATE DATABASE`, `USE`)
- ✅ **Base de données unique** par client avec timestamp pour éviter les conflits
- ✅ **Gestion d'erreurs** complète avec messages détaillés
- ✅ **Vérification post-import** du nombre de tables créées

### Configuration du /etc/hosts

Ajoutez cette ligne à votre fichier `/etc/hosts` :

```
127.0.0.1 localhost api.localhost sync.localhost front.localhost
```

## 🌐 URLs disponibles

| Service | URL | Description |
|---------|-----|-------------|
| **Backoffice Riashop** | http://localhost | Interface d'administration |
| **API (Engine)** | http://api.localhost | API Riashop (dans l'engine) |
| **Sync (Engine)** | http://sync.localhost | API de synchronisation (dans l'engine) |
| **Frontend Client** | http://front.localhost | Site client (si configuré) |
| **PhpMyAdmin** | http://localhost:8080 | Administration BDD |
| **Adminer** | http://localhost:8081 | Administration BDD (alternative) |
| **CouchDB** | http://localhost:5984 | Interface CouchDB |
| **MailHog** | http://localhost:8025 | Interface emails de test |
| **Beanstalk Console** | http://localhost:2080 | Gestion des queues |

## 🔧 Configuration

### Fichier de configuration PHP

La configuration se fait via le fichier `engine/include/env.inc.php` (comme en production) :

```php
<?php
$env = [
    'ENVRIA_TNT_ID' => '1',
    'ENVRIA_BDD_SERVER' => 'mariadb',
    'ENVRIA_COUCHDB_URL' => 'couchdb:5984',
    'ENVRIA_ENGINE_DIRECTORY' => dirname(__FILE__) . '/../',
    // ... autres variables
];
foreach ($env as $key => $value) {
    putenv("$key=$value");
}
```

Le système gère automatiquement :
- Création de `env.inc.php` si absent
- Création de `env.local.inc.php` pour surcharger une config existante
- Inclusion automatique de la config locale

### Base de données

- **Host** : `localhost:3306`
- **Utilisateur** : `riashop`
- **Mot de passe** : `riashop123`
- **Base** : `riashop_db`

### Variables d'environnement

Le fichier `.env` est automatiquement créé dans `www/engine/.env` avec :

```bash
# Identifiants client
ENVRIA_TNT_ID=1
ENVRIA_WST_ID=1

# Base de données
ENVRIA_BDD_SERVER=mariadb
ENVRIA_BDD_LOGIN=riashop
ENVRIA_BDD_PWD=riashop123
ENVRIA_BDD_NAME=riashop_db

# Services
ENVRIA_COUCHDB_URL=couchdb:5984
ENVRIA_QUEUE_SERVER=beanstalk
ENVRIA_API_URL=http://api.localhost
ENVRIA_SYNC_URL=http://sync.localhost
```

## 👥 Gestionnaire de clients

### Mode interactif (recommandé)

```bash
# Création interactive d'un client - Assistant guidé
./client-manager.sh add

# L'assistant vous demandera :
# - Nom du client
# - TNT_ID (ID tenant)
# - Nom de la base de données (optionnel)
# - Fichier SQL à importer (optionnel)
# - URLs des frontends SVN (optionnel, multiple)
```

### Mode traditionnel (avancé)

```bash
# Lister les clients
./client-manager.sh list

# Ajouter un client avec paramètres
./client-manager.sh add CLIENT_NAME \
  --tnt-id TNT_ID \
  --sql-file "/path/to/database.sql" \
  --frontend-svn "URL_FRONTEND_1" \
  --frontend-svn "URL_FRONTEND_2"

# Changer de client actif
./client-manager.sh switch CLIENT_NAME

# Supprimer un client
./client-manager.sh remove CLIENT_NAME

# Voir le client actuel
./client-manager.sh current

# État de tous les clients
./client-manager.sh status
```

### Exemple complet

#### Mode interactif (recommandé)
```bash
# Lancer l'assistant interactif
./client-manager.sh add

# L'assistant vous guidera à travers :
# 1. Nom du client : Sardeco
# 2. TNT_ID : 36
# 3. Base de données : sardeco_db (par défaut)
# 4. Fichier SQL : /home/<USER>/sardeco_database.sql
# 5. Frontend SVN : https://svn.riashop.fr/svn/sardeco/branches/extranet
# 6. Confirmation et création automatique

# Activer le client créé
./client-manager.sh switch Sardeco
```

#### Mode traditionnel
```bash
# Ajouter le client Sardeco avec sa base de données
./client-manager.sh add Sardeco \
  --tnt-id 36 \
  --sql-file "/home/<USER>/sardeco_database.sql" \
  --frontend-svn "https://svn.riashop.fr/svn/sardeco/branches/extranet"

# Activer le client Sardeco
./client-manager.sh switch Sardeco
```

### 🎯 Améliorations du gestionnaire de clients

- ✅ **Mode interactif** - Assistant guidé pour une création simplifiée
- ✅ **URLs SVN hardcodées** - Engine et backoffice automatiquement configurés
- ✅ **Support multi-frontend** - Plusieurs frontends par client
- ✅ **Plus de WST_ID** - Paramètre supprimé car non pertinent lors de la création
- ✅ **Validation intelligente** - Vérification des fichiers et paramètres
- ✅ **Résumé avant création** - Confirmation des paramètres avant traitement

## 📋 Commandes Make

### Installation et gestion

```bash
make help                    # Aide complète
make setup                   # Installation avec paramètres
make start                   # Démarre l'environnement
make stop                    # Arrête l'environnement
make restart                 # Redémarre l'environnement
make status                  # État des conteneurs
```

### Développement

```bash
make logs                    # Logs en temps réel
make logs-apache            # Logs Apache uniquement
make shell                  # Shell dans le conteneur principal
make mysql                  # Accès MySQL/MariaDB
make update-sources         # Met à jour via SVN
```

### Maintenance

```bash
make clean                  # Supprime tout l'environnement
make rebuild               # Reconstruit les images
make reset-db              # Recrée la base de données
make backup                # Sauvegarde la BDD
make restore DB_FILE=...   # Restaure la BDD
```

### Outils

```bash
make test-config           # Teste la config Apache
make reload-apache         # Recharge Apache
make worker-status         # État des workers
make permission-fix        # Corrige les permissions
make urls                  # Affiche les URLs
```

## 🛠️ VirtualHosts Apache

### API (Engine)
- **URL** : http://api.localhost
- **DocumentRoot** : `/var/www/engine/htdocs/api/`
- **Include Path** : `/var/www/engine/include/`
- **Réécriture** vers `index.php`

### Sync (Engine)
- **URL** : http://sync.localhost
- **DocumentRoot** : `/var/www/engine/htdocs/sync/`
- **Include Path** : `/var/www/engine/include/`
- **Réécriture** vers `index.php`

### Backoffice
- **URL** : http://localhost
- **DocumentRoot** : `/var/www/sites/riashop/htdocs/`
- **Include Path** : `/var/www/sites/riashop/include/:/var/www/engine/include/`
- **Auto prepend** : `config.inc.php`

### Frontend Client
- **URL** : http://front.localhost
- **DocumentRoot** : `/var/www/sites/[client]/htdocs/`
- **Include Path** : `/var/www/engine/include/:/var/www/sites/[client]/htdocs/include/`
- **Réécriture** vers `rewritemap.php`

## 🔄 Workers et Queues

### Configuration des workers

Les workers Python sont configurés automatiquement avec :
- **3 niveaux de priorité** : low, high, higher
- **Auto-throttle** : démarrage/arrêt automatique selon la charge
- **Beanstalk** comme gestionnaire de queues

### Gestion des workers

```bash
# État des workers
make worker-status

# Redémarrer les workers
make worker-restart

# Logs des workers
make logs-worker
```

## 🗂️ Gestion multi-clients

### Ajouter un nouveau client

```bash
# Méthode 1 : Réinstallation complète
make setup CLIENT_NAME=nouveauclient \
  ENGINE_SVN_URL="..." \
  BACKOFFICE_SVN_URL="..." \
  FRONTEND_SVN_URL="..."

# Méthode 2 : Installation rapide (si engine/backoffice déjà présents)
make install-client CLIENT_NAME=nouveauclient \
  FRONTEND_SVN_URL="..."
```

### Structure multi-clients

```
www/sites/
├── riashop/              # Backoffice (commun)
├── client1/              # Frontend client 1
├── client2/              # Frontend client 2  
└── client3/              # Frontend client 3
```

## 🔍 Débogage

### Logs courants

```bash
# Tous les logs
make logs

# Logs Apache uniquement
make logs-apache

# Logs d'un service spécifique
docker-compose logs riashop-main
docker-compose logs mariadb
docker-compose logs riashop-worker
```

### Accès aux conteneurs

```bash
# Shell principal
make shell

# Shell worker
make shell-worker

# MySQL
make mysql
```

### Vérifications

```bash
# Test configuration Apache
make test-config

# État des conteneurs
make status

# Permissions
make permission-fix
```

## 📊 Monitoring

### Services disponibles

- **Apache** : Port 80/443
- **MariaDB** : Port 3306
- **CouchDB** : Port 5984
- **Memcache** : Port 11211
- **Beanstalk** : Port 11300
- **PhpMyAdmin** : Port 8080

### Health checks

```bash
# État général
docker-compose ps

# Logs des erreurs
make logs-apache | grep ERROR

# Test des services
curl http://localhost        # Backoffice
curl http://api.localhost    # API
curl http://sync.localhost   # Sync
```

## 🚨 Résolution de problèmes

### Problèmes courants

1. **Erreur 404 sur les URLs**
   ```bash
   make hosts-info  # Vérifier /etc/hosts
   make test-config # Tester config Apache
   ```

2. **Erreurs de permissions**
   ```bash
   make permission-fix
   ```

3. **Base de données inaccessible**
   ```bash
   make reset-db
   ```

4. **Échec d'import de base de données**
   ```bash
   # Vérifier que MariaDB est démarré
   docker-compose ps mariadb

   # Vérifier les logs MariaDB
   docker-compose logs riashop-mariadb

   # Tester la connexion
   docker exec riashop-mariadb mysql -u root -proot123 -e "SHOW DATABASES;"

   # Import manuel si nécessaire
   ./scripts/database-import.sh "/path/to/file.sql" "database_name" "client_name"
   ```

5. **Fichier SQL trop volumineux**
   ```bash
   # Vérifier l'espace disque disponible
   df -h

   # Augmenter les limites MySQL si nécessaire
   docker exec riashop-mariadb mysql -u root -proot123 -e "
   SET GLOBAL max_allowed_packet=1073741824;
   SET GLOBAL innodb_buffer_pool_size=2147483648;"
   ```

6. **Workers non fonctionnels**
   ```bash
   make worker-restart
   make worker-status
   ```

### Réinitialisation complète

```bash
make clean    # Supprime tout
make setup    # Réinstalle
```

## 📝 Notes importantes

- **Engine non accessible par URL** : Le moteur n'est accessible que localement, pas via une URL directe
- **API/Sync dans l'engine** : Les endpoints API et Sync sont des sous-dossiers de l'engine avec des VirtualHosts dédiés
- **Structure production respectée** : L'arborescence correspond exactement à l'architecture V3
- **SVN automatique** : Les sources sont automatiquement téléchargées et mises à jour via SVN
- **Multi-clients** : Support natif pour plusieurs clients avec frontends séparés# riashop-v2
