# =============================================================================
# RIASHOP LOCAL V2 - GITIGNORE
# =============================================================================
# This file ensures that SVN content, client data, and runtime files are never
# committed to the GitHub repository while preserving the boilerplate structure.

# =============================================================================
# SVN CONTENT - NEVER COMMIT
# =============================================================================
# The entire www/ directory contains SVN checkouts and should never be committed
www/

# SVN-specific files and directories (in case they appear elsewhere)
.svn/
*.svn-*
.svn-*

# =============================================================================
# CLIENT-SPECIFIC DATA - NEVER COMMIT
# =============================================================================
# Client configuration files (contain sensitive data like TNT_ID, database names)
clients/*/
clients/*.conf

# Client-specific generated environment files
**/env.local.inc.php
**/env.local.php

# =============================================================================
# RUNTIME DATA - NEVER COMMIT
# =============================================================================
# Logs directory and log files
logs/error_log
logs/access_log
logs/*.log
logs/*.log.*
*.log
*.log.*

# Backup files and directories
backups/
*.backup
*.bak
*.sql.gz
*.sql.bz2

# Database dump files (contain sensitive data)
databases/*.sql
databases/*.sql.gz
databases/*.sql.bz2

# Temporary files
*.tmp
*.temp
*~
.DS_Store
Thumbs.db

# =============================================================================
# DOCKER RUNTIME DATA - NEVER COMMIT
# =============================================================================
# Docker volumes and runtime data
docker-compose.override.yml
.env.local

# =============================================================================
# DEVELOPMENT FILES - NEVER COMMIT
# =============================================================================
# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# =============================================================================
# KEEP THESE BOILERPLATE FILES - ALWAYS COMMIT
# =============================================================================
# The following files/directories should always be committed:
# - README.md
# - Makefile
# - setup.sh
# - client-manager.sh
# - docker-compose.yml
# - docker/ (directory structure and Dockerfiles)
# - .env.example (template file)
# - .gitignore (this file)

# =============================================================================
# EXCEPTIONS - FORCE INCLUDE IMPORTANT BOILERPLATE STRUCTURE
# =============================================================================
# Force include empty directories that are part of the boilerplate structure
!logs/.gitkeep
!backups/.gitkeep
!config/.gitkeep
!www/.gitkeep
!databases/.gitkeep
!databases/README.md

# Force include docker configuration files
!docker/
!docker/**

# Force include template and example files
!.env.example
!*.example
!*.template
