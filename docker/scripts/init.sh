#!/bin/bash

echo "🚀 Initialisation du conteneur Riashop..."

# Activation des sites Apache
echo "📝 Configuration Apache..."
a2ensite api
a2ensite sync
a2ensite backoffice
if [ -f /etc/apache2/sites-available/frontend.conf ]; then
    a2ensite frontend
fi

# Désactivation du site par défaut
a2dissite 000-default

# Rechargement d'Apache
service apache2 reload

# Gestion du fichier env.inc.php
if [ -d /var/www/engine/include ]; then
    if [ ! -f /var/www/engine/include/env.inc.php ] && [ -f /etc/riashop/env.inc.php.template ]; then
        echo "Création du fichier env.inc.php par défaut..."
        cp /etc/riashop/env.inc.php.template /var/www/engine/include/env.inc.php
    elif [ -f /etc/riashop/env.inc.php.template ]; then
        echo "Le fichier env.inc.php existe, création de env.local.inc.php..."
        cp /etc/riashop/env.inc.php.template /var/www/engine/include/env.local.inc.php
        # Vérifier si env.local.inc.php est déjà inclus dans env.inc.php
        if ! grep -q "env.local.inc.php" /var/www/engine/include/env.inc.php; then
            echo "Ajout de l'inclusion de env.local.inc.php..."
            echo "" >> /var/www/engine/include/env.inc.php
            echo "// Configuration locale Docker" >> /var/www/engine/include/env.inc.php
            echo "if (file_exists(dirname(__FILE__) . '/env.local.inc.php')) {" >> /var/www/engine/include/env.inc.php
            echo "    include_once dirname(__FILE__) . '/env.local.inc.php';" >> /var/www/engine/include/env.inc.php
            echo "}" >> /var/www/engine/include/env.inc.php
        fi
    fi
fi

# Configuration des crons si nécessaire
if [ -d /var/www/engine ]; then
    # Liens symboliques pour les crons
    if [ -f /var/www/engine/cron.daily ]; then
        ln -sf /var/www/engine/cron.daily /etc/cron.daily/riashop
    fi
    if [ -f /var/www/engine/cron.hourly ]; then
        ln -sf /var/www/engine/cron.hourly /etc/cron.hourly/riashop
    fi
    if [ -f /var/www/engine/cron.monthly ]; then
        ln -sf /var/www/engine/cron.monthly /etc/cron.monthly/riashop
    fi
    if [ -f /var/www/engine/cron.others ]; then
        ln -sf /var/www/engine/cron.others /etc/cron.d/riashop
    fi
fi

# Permissions
chown -R www-data:www-data /var/www
chmod -R 755 /var/www

echo "Initialisation terminée"
exec "$@"
