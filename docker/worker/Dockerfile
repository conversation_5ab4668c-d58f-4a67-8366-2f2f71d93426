# Dockerfile pour les workers Riashop
FROM python:3.9-slim

# Installation des dépendances système
RUN apt-get update && apt-get install -y \
    php5.6-cli \
    php5.6-mysql \
    php5.6-curl \
    php5.6-json \
    php5.6-xml \
    && rm -rf /var/lib/apt/lists/*

# Installation des dépendances Python pour les workers
RUN pip install \
    pystalkd \
    mysql-connector-python \
    requests

# Création du répertoire de travail
WORKDIR /opt/riashop-worker

# Copie des fichiers de configuration
COPY docker/worker/config.json /opt/riashop-worker/config.json
COPY docker/worker/start.sh /opt/riashop-worker/start.sh

# Permissions
RUN chmod +x /opt/riashop-worker/start.sh

# Variables d'environnement
ENV PYTHONPATH=/opt/riashop-worker
ENV PHP_EXECUTABLE=/usr/bin/php5.6

# Commande par défaut
CMD ["/opt/riashop-worker/start.sh"]
