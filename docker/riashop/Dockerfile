# Dockerfile pour le service principal Riashop
# PHP 5.6 requis pour compatibilité avec le code legacy Riashop
FROM php:5.6-apache

# Configuration des dépôts Debian Stretch (archivés)
RUN echo "deb http://archive.debian.org/debian stretch main" > /etc/apt/sources.list && \
    echo "deb http://archive.debian.org/debian-security stretch/updates main" >> /etc/apt/sources.list && \
    echo "Acquire::Check-Valid-Until false;" > /etc/apt/apt.conf.d/99no-check-valid-until

# Installation des dépendances système
RUN apt-get update && apt-get install -y --allow-unauthenticated \
    libmemcached-dev \
    libmcrypt-dev \
    libxml2-dev \
    libzip-dev \
    zip \
    unzip \
    git \
    curl \
    cron \
    supervisor \
    python3 \
    python3-pip \
    && rm -rf /var/lib/apt/lists/*

# Installation des extensions PHP requises pour PHP 5.6
RUN docker-php-ext-install \
    mysqli \
    pdo_mysql \
    mcrypt \
    xml \
    zip \
    gettext

# Activation de l'extension mysql legacy pour PHP 5.6
RUN docker-php-ext-configure mysql --with-mysql=mysqlnd \
    && docker-php-ext-install mysql

# Installation de l'extension memcached compatible PHP 5.6
RUN pecl install memcached-2.2.0 \
    && docker-php-ext-enable memcached

# Configuration d'Apache
COPY docker/apache/sites-available/ /etc/apache2/sites-available/
RUN a2enmod rewrite ssl headers
RUN a2dissite 000-default
RUN a2ensite backoffice.conf frontend.conf api.conf sync.conf

# Création des répertoires nécessaires selon la vraie structure de production
RUN mkdir -p /var/www/engine \
    && mkdir -p /var/www/sites/riashop \
    && mkdir -p /var/www/media \
    && mkdir -p /apps/instances \
    && mkdir -p /var/log/riashop \
    && mkdir -p /etc/riashop

# Configuration des permissions
RUN chown -R www-data:www-data /var/www \
    && chown -R www-data:www-data /apps/instances \
    && chown -R www-data:www-data /var/log/riashop

# Configuration Supervisor pour gérer les processus
COPY docker/supervisor/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Script d'initialisation
COPY docker/scripts/init.sh /usr/local/bin/init.sh
RUN chmod +x /usr/local/bin/init.sh

# Variables d'environnement par défaut
ENV APACHE_DOCUMENT_ROOT=/var/www
ENV APACHE_RUN_USER=www-data
ENV APACHE_RUN_GROUP=www-data
ENV APACHE_LOG_DIR=/var/log/apache2

EXPOSE 80 443

# Démarrage avec supervisor
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]