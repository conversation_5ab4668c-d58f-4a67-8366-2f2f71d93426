-- Initialisation de la base Riashop
CREATE DATABASE IF NOT EXISTS riashop_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE riashop_db;

-- Tables de base (à adapter selon vos besoins)
CREATE TABLE IF NOT EXISTS ria_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(255) NOT NULL,
    config_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insertion de quelques valeurs par défaut
INSERT INTO ria_config (config_key, config_value) VALUES
('version', '1.0.0'),
('environment', 'local'),
('debug', 'true');
