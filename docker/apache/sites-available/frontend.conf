<VirtualHost *:80>
    ServerName front.localhost
    ServerAlias www.front.localhost
    DocumentRoot /var/www/sites/Sardeco/htdocs/

    <Directory /var/www/sites/Sardeco/htdocs/>
        Options FollowSymlinks
        AllowOverride All
        Require all granted
    </Directory>

    AcceptPathInfo  On
    RewriteEngine   On
    php_value error_reporting 22519

    # Règles de réécriture pour les ressources statiques
    RewriteCond     %{REQUEST_URI}  !^/css.*
    RewriteCond     %{REQUEST_URI}  !^/images.*
    RewriteCond     %{REQUEST_URI}  !^/admin.*
    RewriteCond     %{REQUEST_URI}  !^/js.*
    RewriteCond     %{REQUEST_URI}  !^/video/.*
    RewriteCond     %{REQUEST_URI}  !^/favicon.ico$
    RewriteCond     %{REQUEST_URI}  !^/robots.txt$
    RewriteCond     %{REQUEST_URI}  !^/sync/
    RewriteCond     %{REQUEST_URI}  !^/catalogue-en-ligne/
    RewriteCond     %{REQUEST_URI}  !^/.well-known/
    Rewriterule     (.*)            /rewritemap.php       [L,QSA]

    php_admin_value  include_path        "/var/www/engine/include/:/var/www/sites/Sardeco/htdocs/include/"
    php_admin_value  auto_prepend_file   "/var/www/sites/Sardeco/htdocs/config.inc.php"
    php_admin_value  session.cookie_lifetime   604800
    php_admin_value  session.gc_maxlifetime    604800
    php_admin_value  session.auto_start        1

    # Logs centralisés dans le répertoire monté sur l'hôte
    ErrorLog /var/log/riashop/error_log
    CustomLog /var/log/riashop/access_log combined
</VirtualHost>
