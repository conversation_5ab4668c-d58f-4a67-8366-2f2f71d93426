<VirtualHost *:80>
    ServerName sync.localhost
    ServerAlias www.sync.localhost
    DocumentRoot /var/www/engine/htdocs/sync/

    <Directory /var/www/engine/htdocs/sync/>
        Options -Indexes +FollowSymLinks -MultiViews
        AllowOverride All
        Require all granted
    </Directory>

    php_flag display_startup_errors off
    php_flag display_errors on
    php_value error_reporting 22519

    php_admin_value   include_path          "/var/www/engine/include/"

    ## Specific riashop
    AcceptPathInfo  On
    RewriteEngine   On

    RewriteCond     %{REQUEST_URI}  !^/request.php
    RewriteCond     %{REQUEST_URI}  !^/robots.txt$
    RewriteRule     (.*)            /index.php$1       [L,QSA]

    php_admin_value   max_execution_time    600
    php_admin_value   max_input_time        600
    php_admin_value   max_input_vars        60000
    php_admin_value   upload_max_filesize   "400M"
    php_admin_value   post_max_size         "400M"

    # Ajout d'une directive pour éviter les conflits
    SetEnvIf Host "^sync.localhost$" SYNC_ENV

    # Logs centralisés dans le répertoire monté sur l'hôte
    ErrorLog /var/log/riashop/error_log
    CustomLog /var/log/riashop/access_log combined
</VirtualHost>
