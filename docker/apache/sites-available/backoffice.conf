<VirtualHost *:80>
    ServerName localhost
    ServerAlias www.localhost
    DocumentRoot /var/www/sites/riashop/htdocs/

    <Directory /var/www/sites/riashop/htdocs/>
        Options -Indexes +FollowSymLinks -MultiViews
        AllowOverride All
        Require all granted
        php_admin_value include_path "/var/www/sites/riashop/include/:/var/www/engine/include/"
        php_value auto_prepend_file "/var/www/sites/riashop/htdocs/config.inc.php"
        php_admin_value session.auto_start 1
        php_admin_value default_charset "utf-8"
    </Directory>

    php_flag display_startup_errors off
    php_flag display_errors on
    php_value error_reporting 22519

    # Ajout d'une directive pour éviter les conflits
    SetEnvIf Host "^localhost$" BACKOFFICE_ENV

    # Logs centralisés dans le répertoire monté sur l'hôte
    ErrorLog /var/log/riashop/error_log
    CustomLog /var/log/riashop/access_log combined
</VirtualHost>
