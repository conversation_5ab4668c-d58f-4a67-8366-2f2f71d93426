# This file ensures the databases/ directory is preserved in the repository
# The databases/ directory is used to store client SQL dump files for database initialization
# 
# Usage:
# - Place client SQL dump files here as: [CLIENT_NAME].sql
# - Example: Sardeco.sql, MyClient.sql, etc.
# - These files will be automatically imported when adding clients with --sql-file parameter
#
# Note: Actual SQL dump files are excluded from git to avoid committing sensitive data
