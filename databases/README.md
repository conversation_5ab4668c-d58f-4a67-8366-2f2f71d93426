# Database Management

This directory contains SQL dump files for client database initialization.

## Directory Structure

```
databases/
├── README.md           # This file
├── .gitkeep           # Preserves directory in git
└── [CLIENT_NAME].sql  # Client database dumps (excluded from git)
```

## Usage

### 1. Adding Client Database Dumps

Place your client SQL dump files in this directory using the naming convention:
```
[CLIENT_NAME].sql
```

Examples:
- `Sardeco.sql` - Database dump for Sardeco client
- `MyClient.sql` - Database dump for MyClient
- `TestClient.sql` - Database dump for TestClient

### 2. Using Database Dumps with Client Manager

When adding a new client, specify the SQL file path:

```bash
# Using absolute path
./client-manager.sh add Sardeco --tnt-id 36 --wst-id 1 \
  --engine-svn "https://svn.example.com/engine" \
  --backoffice-svn "https://svn.example.com/backoffice" \
  --frontend-svn "https://svn.example.com/frontend" \
  --sql-file "/path/to/sardecou0001.sql"

# Using relative path from project root
./client-manager.sh add Sardeco --tnt-id 36 --wst-id 1 \
  --engine-svn "https://svn.example.com/engine" \
  --backoffice-svn "https://svn.example.com/backoffice" \
  --frontend-svn "https://svn.example.com/frontend" \
  --sql-file "databases/Sardeco.sql"
```

### 3. Database Import Process

The client manager will:
1. Validate that the SQL file exists
2. Create the client's unique database
3. Import the SQL dump into the database
4. Provide feedback on import success/failure

## Security Notes

- **SQL dump files are excluded from git** to prevent committing sensitive data
- Only place SQL dumps in this directory temporarily during setup
- Consider using environment-specific dumps (development data vs production structure)
- Remove or secure SQL dumps after successful import

## Troubleshooting

### Import Fails
- Check that MariaDB container is running: `docker-compose ps`
- Verify SQL file syntax and encoding
- Check container logs: `docker-compose logs riashop-mariadb`

### Permission Issues
- Ensure SQL file is readable: `chmod 644 databases/[CLIENT_NAME].sql`
- Check Docker volume mounts are working

### Large SQL Files
- For very large dumps, consider increasing Docker memory limits
- Monitor import progress in MariaDB logs
