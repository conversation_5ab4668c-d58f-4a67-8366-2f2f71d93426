version: '3.8'

services:
  # Service principal Riashop (équivalent au LXC unique de la V3)
  riashop-main:
    build:
      context: .
      dockerfile: docker/riashop/Dockerfile
    container_name: riashop-main
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./www:/var/www:rw
      - ./config:/etc/riashop:rw
      - ./logs:/var/log/riashop:rw
      - riashop-data:/apps/instances
    environment:
      - ENVRIA_TNT_ID=1
      - ENVRIA_WST_ID=1
      - ENVRIA_BDD_SERVER=mariadb
      - ENVRIA_BDD_LOGIN=riashop
      - ENVRIA_BDD_PWD=riashop123
      - ENVRIA_BDD_NAME=riashop_db
      - ENVRIA_PACKAGE=business
      - ENVRIA_COUCHDB_PROTOCOL=http
      - ENVRIA_COUCHDB_URL=couchdb:5984
      - ENVRIA_COUCHDB_LOGIN=admin
      - ENVRIA_COUCHDB_PASSWORD=admin123
      - ENVRIA_QUEUE_SERVER=beanstalk
      - ENVRIA_QUEUE_PREFIX=local-dev
      - ENVRIA_API_URL=http://api.localhost
      - ENVRIA_SYNC_URL=http://sync.localhost
      - ENVRIA_ENGINE_DIRECTORY=/var/www/engine/
    depends_on:
      - mariadb
      - couchdb
      - memcache
      - beanstalk
    networks:
      - riashop-network
    extra_hosts:
      - "localhost:127.0.0.1"
      - "api.localhost:127.0.0.1"
      - "sync.localhost:127.0.0.1"
      - "front.localhost:127.0.0.1"

  # Base de données MariaDB
  mariadb:
    image: mariadb:10.6
    container_name: riashop-mariadb
    environment:
      - MYSQL_ROOT_PASSWORD=root123
      - MYSQL_DATABASE=riashop_db
      - MYSQL_USER=riashop
      - MYSQL_PASSWORD=riashop123
    volumes:
      - mariadb-data:/var/lib/mysql
      - ./docker/mariadb/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "3306:3306"
    networks:
      - riashop-network

  # CouchDB pour le stockage de documents JSON
  couchdb:
    image: couchdb:3.3
    container_name: riashop-couchdb
    environment:
      - COUCHDB_USER=admin
      - COUCHDB_PASSWORD=admin123
    volumes:
      - couchdb-data:/opt/couchdb/data
    ports:
      - "5984:5984"
    networks:
      - riashop-network

  # Memcache pour la mise en cache
  memcache:
    image: memcached:1.6-alpine
    container_name: riashop-memcache
    command: memcached -m 256
    ports:
      - "11211:11211"
    networks:
      - riashop-network

  # Beanstalk pour la gestion des queues (équivalent RabbitMQ)
  beanstalk:
    image: schickling/beanstalkd
    container_name: riashop-beanstalk
    ports:
      - "11300:11300"
    networks:
      - riashop-network

  # Workers pour traitement des tâches asynchrones (temporairement désactivé)
  # riashop-worker:
  #   build:
  #     context: .
  #     dockerfile: docker/worker/Dockerfile
  #   container_name: riashop-worker
  #   volumes:
  #     - ./www:/var/www:ro
  #     - ./worker-config:/opt/riashop-worker:rw
  #   environment:
  #     - ENVRIA_QUEUE_SERVER=beanstalk
  #     - ENVRIA_QUEUE_PREFIX=local-dev
  #   depends_on:
  #     - beanstalk
  #     - mariadb
  #     - couchdb
  #   networks:
  #     - riashop-network

  # PHPMyAdmin pour l'administration de la base
  phpmyadmin:
    image: phpmyadmin:5.2
    container_name: riashop-phpmyadmin
    environment:
      - PMA_HOST=mariadb
      - PMA_USER=riashop
      - PMA_PASSWORD=riashop123
    ports:
      - "8080:80"
    depends_on:
      - mariadb
    networks:
      - riashop-network

  # MailHog pour les tests d'emails en développement
  mailhog:
    image: mailhog/mailhog:latest
    container_name: riashop-mailhog
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - riashop-network

  # Adminer comme alternative légère à phpMyAdmin
  adminer:
    image: adminer:4.8.1
    container_name: riashop-adminer
    ports:
      - "8081:8080"
    depends_on:
      - mariadb
    networks:
      - riashop-network

  # Interface web pour Beanstalk (gestion des queues)
  beanstalk-console:
    image: schickling/beanstalkd-console
    container_name: riashop-beanstalk-console
    ports:
      - "2080:2080"
    depends_on:
      - beanstalk
    networks:
      - riashop-network

volumes:
  mariadb-data:
  couchdb-data:
  riashop-data:

networks:
  riashop-network:
    driver: bridge