# Makefile pour l'environnement Riashop Docker local
.PHONY: help setup start stop restart logs clean update-sources

# Variables par défaut
CLIENT_NAME ?= demo
ENGINE_SVN_URL ?=
BACKOFFICE_SVN_URL ?=
FRONTEND_SVN_URL ?=

help: ## Affiche cette aide
	@echo "Commandes disponibles pour l'environnement Riashop Docker :"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'
	@echo ""
	@echo "Variables d'environnement :"
	@echo "  CLIENT_NAME         Nom du client (défaut: demo)"
	@echo "  ENGINE_SVN_URL      URL SVN du engine"
	@echo "  BACKOFFICE_SVN_URL  URL SVN du backoffice"
	@echo "  FRONTEND_SVN_URL    URL SVN du frontend (optionnel)"
	@echo ""
	@echo "Exemples :"
	@echo "  make setup CLIENT_NAME=myclient ENGINE_SVN_URL=https://... BACKOFFICE_SVN_URL=https://..."
	@echo "  make logs"
	@echo "  make restart"

setup: ## Installation complète de l'environnement
	@if [ -z "$(ENGINE_SVN_URL)" ] || [ -z "$(BACKOFFICE_SVN_URL)" ]; then \
		echo "❌ ENGINE_SVN_URL et BACKOFFICE_SVN_URL sont obligatoires"; \
		echo "Exemple: make setup ENGINE_SVN_URL=https://... BACKOFFICE_SVN_URL=https://... CLIENT_NAME=myclient"; \
		exit 1; \
	fi
	chmod +x setup.sh
	./setup.sh --engine "$(ENGINE_SVN_URL)" --backoffice "$(BACKOFFICE_SVN_URL)" $(if $(FRONTEND_SVN_URL),--frontend "$(FRONTEND_SVN_URL)") --client "$(CLIENT_NAME)"

start: ## Démarre les conteneurs
	docker-compose up -d

stop: ## Arrête les conteneurs
	docker-compose down

restart: ## Redémarre les conteneurs
	docker-compose restart

logs: ## Affiche les logs en temps réel
	docker-compose logs -f

logs-apache: ## Affiche les logs Apache uniquement
	docker-compose exec riashop-main tail -f /var/log/apache2/*.log

logs-worker: ## Affiche les logs des workers
	docker-compose logs -f riashop-worker

status: ## Affiche l'état des conteneurs
	docker-compose ps

shell: ## Accès shell au conteneur principal
	docker-compose exec riashop-main bash

shell-worker: ## Accès shell au conteneur worker
	docker-compose exec riashop-worker bash

mysql: ## Accès MySQL/MariaDB
	docker-compose exec mariadb mysql -u riashop -priashop123 riashop_db

update-sources: ## Met à jour les sources SVN
	@if [ -d "www/engine/.svn" ]; then \
		echo "🔄 Mise à jour de l'engine..."; \
		cd www/engine && svn update; \
	fi
	@if [ -d "www/sites/riashop/.svn" ]; then \
		echo "🔄 Mise à jour du backoffice..."; \
		cd www/sites/riashop && svn update; \
	fi
	@if [ -d "www/sites/$(CLIENT_NAME)/.svn" ]; then \
		echo "🔄 Mise à jour du frontend..."; \
		cd www/sites/$(CLIENT_NAME) && svn update; \
	fi
	@echo "✅ Sources mises à jour"

clean: ## Supprime complètement l'environnement
	docker-compose down -v
	docker system prune -f
	sudo rm -rf www/ logs/ config/ worker-config/

rebuild: ## Reconstruit les images Docker
	docker-compose build --no-cache

reset-db: ## Recrée la base de données
	docker-compose stop mariadb
	docker volume rm riashop-docker_mariadb-data || true
	docker-compose up -d mariadb
	@echo "⏳ Attente de la base de données..."
	sleep 10
	@echo "✅ Base de données recréée"

hosts-info: ## Affiche les entrées à ajouter au fichier /etc/hosts
	@echo "Ajoutez cette ligne à votre fichier /etc/hosts :"
	@echo "127.0.0.1 localhost api.localhost sync.localhost front.localhost"

urls: ## Affiche les URLs disponibles
	@echo "🌐 URLs disponibles :"
	@echo "   - Backoffice Riashop: http://localhost"
	@echo "   - API (engine): http://api.localhost"
	@echo "   - Sync (engine): http://sync.localhost"
	@echo "   - Frontend client: http://front.localhost"
	@echo "   - PhpMyAdmin: http://localhost:8080"
	@echo "   - CouchDB: http://localhost:5984"

backup: ## Sauvegarde la base de données
	@mkdir -p backups
	@echo "💾 Sauvegarde de la base de données..."
	docker-compose exec mariadb mysqldump -u riashop -priashop123 riashop_db > backups/riashop_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "✅ Sauvegarde créée dans backups/"

restore: ## Restaure la base de données (DB_FILE=chemin/vers/fichier.sql)
	@if [ -z "$(DB_FILE)" ]; then \
		echo "❌ Spécifiez le fichier avec DB_FILE=chemin/vers/fichier.sql"; \
		exit 1; \
	fi
	@echo "🔄 Restauration de la base de données depuis $(DB_FILE)..."
	docker-compose exec -T mariadb mysql -u riashop -priashop123 riashop_db < $(DB_FILE)
	@echo "✅ Base de données restaurée"

dev-tools: ## Installe des outils de développement supplémentaires
	docker-compose exec riashop-main apt-get update
	docker-compose exec riashop-main apt-get install -y vim nano htop

test-config: ## Teste la configuration Apache
	docker-compose exec riashop-main apache2ctl configtest

reload-apache: ## Recharge la configuration Apache
	docker-compose exec riashop-main apache2ctl graceful

worker-status: ## Affiche l'état des workers
	docker-compose exec riashop-worker python3 /opt/riashop-worker/console.py --command=status

worker-restart: ## Redémarre les workers
	docker-compose restart riashop-worker

cron-list: ## Liste les tâches cron actives
	docker-compose exec riashop-main crontab -l

permission-fix: ## Corrige les permissions des fichiers
	docker-compose exec riashop-main chown -R www-data:www-data /var/www
	docker-compose exec riashop-main chmod -R 755 /var/www

client-list: ## Liste tous les clients configurés
	./client-manager.sh list

client-add: ## Ajoute un nouveau client (CLIENT_NAME TNT_ID WST_ID requis)
	@if [ -z "$(CLIENT_NAME)" ] || [ -z "$(TNT_ID)" ] || [ -z "$(WST_ID)" ]; then \
		echo "❌ Variables requises: CLIENT_NAME, TNT_ID, WST_ID"; \
		echo "Exemple: make client-add CLIENT_NAME=myclient TNT_ID=123 WST_ID=456"; \
		exit 1; \
	fi
	./client-manager.sh add $(CLIENT_NAME) --tnt-id $(TNT_ID) --wst-id $(WST_ID) \
		$(if $(DB_NAME),--db-name $(DB_NAME)) \
		$(if $(ENGINE_SVN_URL),--engine-svn "$(ENGINE_SVN_URL)") \
		$(if $(BACKOFFICE_SVN_URL),--backoffice-svn "$(BACKOFFICE_SVN_URL)") \
		$(if $(FRONTEND_SVN_URL),--frontend-svn "$(FRONTEND_SVN_URL)") \
		$(if $(CRONS_ENABLED),--crons-enabled $(CRONS_ENABLED))

client-switch: ## Switch vers un client (CLIENT_NAME requis)
	@if [ -z "$(CLIENT_NAME)" ]; then \
		echo "❌ CLIENT_NAME requis"; \
		echo "Exemple: make client-switch CLIENT_NAME=myclient"; \
		exit 1; \
	fi
	./client-manager.sh switch $(CLIENT_NAME)

client-current: ## Affiche le client actuel
	./client-manager.sh current

client-remove: ## Supprime un client (CLIENT_NAME requis)
	@if [ -z "$(CLIENT_NAME)" ]; then \
		echo "❌ CLIENT_NAME requis"; \
		exit 1; \
	fi
	./client-manager.sh remove $(CLIENT_NAME)

client-backup: ## Sauvegarde un client (CLIENT_NAME requis)
	@if [ -z "$(CLIENT_NAME)" ]; then \
		echo "❌ CLIENT_NAME requis"; \
		exit 1; \
	fi
	./client-manager.sh backup $(CLIENT_NAME)

client-status: ## Statut de tous les clients
	./client-manager.sh status

crons-enable: ## Active les crons pour le client actuel (⚠️  DANGER PROD)
	@read -p "⚠️  Activer les crons peut impacter la production! Continuer? (y/N) " confirm && [ "$confirm" = "y" ]
	@current_client=$(./client-manager.sh current | grep "Client actuel:" | cut -d: -f2 | tr -d ' ') && \
	if [ -n "$current_client" ]; then \
		sed -i 's/CRONS_ENABLED="false"/CRONS_ENABLED="true"/' ./clients/$current_client.conf && \
		./client-manager.sh switch $current_client; \
	else \
		echo "❌ Aucun client actuel"; \
	fi

crons-disable: ## Désactive les crons pour le client actuel
	@current_client=$(./client-manager.sh current | grep "Client actuel:" | cut -d: -f2 | tr -d ' ') && \
	if [ -n "$current_client" ]; then \
		sed -i 's/CRONS_ENABLED="true"/CRONS_ENABLED="false"/' ./clients/$current_client.conf && \
		./client-manager.sh switch $current_client; \
	else \
		echo "❌ Aucun client actuel"; \
	fi