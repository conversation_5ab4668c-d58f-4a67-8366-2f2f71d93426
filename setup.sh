#!/bin/bash

# Script d'installation de l'environnement Riashop Docker local
# Basé sur l'architecture V3 de production avec setup automatique par client

set -e

echo "🚀 Installation de l'environnement Riashop Docker local"
echo "========================================================"

# Configuration par défaut (modifiable)
ENGINE_SVN_URL=""
BACKOFFICE_SVN_URL=""
FRONTEND_SVN_URL=""
CLIENT_NAME="demo"

# Fonction d'aide
show_help() {
    cat << EOF
Usage: $0 [OPTIONS]

Options:
    -e, --engine URL        URL SVN du engine (obligatoire)
    -b, --backoffice URL    URL SVN du backoffice/riashop (obligatoire)
    -f, --frontend URL      URL SVN du frontend/extranet client (optionnel)
    -c, --client NAME       Nom du client (défaut: demo)
    -h, --help              Afficher cette aide

Exemple:
    $0 -e "https://svn.example.com/engine" -b "https://svn.example.com/backoffice" -f "https://svn.example.com/client-front" -c "myclient"
EOF
}

# Parse des arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--engine)
            ENGINE_SVN_URL="$2"
            shift 2
            ;;
        -b|--backoffice)
            BACKOFFICE_SVN_URL="$2"
            shift 2
            ;;
        -f|--frontend)
            FRONTEND_SVN_URL="$2"
            shift 2
            ;;
        -c|--client)
            CLIENT_NAME="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "Option inconnue: $1"
            show_help
            exit 1
            ;;
    esac
done

# Vérification des prérequis
if ! command -v docker &> /dev/null; then
    echo "❌ Docker n'est pas installé. Veuillez l'installer d'abord."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose n'est pas installé. Veuillez l'installer d'abord."
    exit 1
fi

if ! command -v svn &> /dev/null; then
    echo "❌ SVN n'est pas installé. Veuillez l'installer d'abord."
    exit 1
fi

if [[ -z "$ENGINE_SVN_URL" ]] || [[ -z "$BACKOFFICE_SVN_URL" ]]; then
    echo "❌ Les URLs SVN pour l'engine et le backoffice sont obligatoires"
    show_help
    exit 1
fi

echo "✅ Prérequis vérifiés"
echo "🔧 Configuration client: $CLIENT_NAME"

# Création de la structure de dossiers selon la production
echo "📁 Création de la structure de dossiers..."
mkdir -p {config,logs,docker/{riashop,worker,apache/sites-available,supervisor,scripts,mariadb},worker-config}

# Structure www selon la vraie production
mkdir -p www/{engine,media,sites/{riashop,$CLIENT_NAME}}

echo "✅ Structure de dossiers créée"

# Téléchargement des sources via SVN
echo "📥 Téléchargement des sources..."

echo "  📦 Engine depuis $ENGINE_SVN_URL"
if [[ -d "www/engine/.svn" ]]; then
    echo "    Engine déjà présent, mise à jour..."
    cd www/engine && svn update && cd ../..
else
    svn checkout "$ENGINE_SVN_URL" www/engine
fi

echo "  📦 Backoffice depuis $BACKOFFICE_SVN_URL"
if [[ -d "www/sites/riashop/.svn" ]]; then
    echo "    Backoffice déjà présent, mise à jour..."
    cd www/sites/riashop && svn update && cd ../../..
else
    svn checkout "$BACKOFFICE_SVN_URL" www/sites/riashop
fi

if [[ -n "$FRONTEND_SVN_URL" ]]; then
    echo "  📦 Frontend client depuis $FRONTEND_SVN_URL"
    if [[ -d "www/sites/$CLIENT_NAME/.svn" ]]; then
        echo "    Frontend déjà présent, mise à jour..."
        cd "www/sites/$CLIENT_NAME" && svn update && cd ../../..
    else
        svn checkout "$FRONTEND_SVN_URL" "www/sites/$CLIENT_NAME"
    fi
fi

echo "✅ Sources téléchargées"

# Configuration Apache avec les VirtualHosts corrects
echo "🌐 Configuration d'Apache..."

# VirtualHost pour API (dans l'engine)
cat > docker/apache/sites-available/api.conf << 'EOF'
<VirtualHost *:80>
    ServerName api.localhost
    ServerAlias www.api.localhost
    DocumentRoot /var/www/engine/htdocs/api/

    <Directory /var/www/engine/htdocs/api/>
        Options -Indexes +FollowSymLinks -MultiViews
        AllowOverride All
        Require all granted
    </Directory>

    php_flag display_startup_errors off
    php_flag display_errors on
    php_value error_reporting 22519

    php_admin_value   include_path          "/var/www/engine/include/"

    ## Specific riashop
    AcceptPathInfo  On
    RewriteEngine   On

    RewriteCond     %{REQUEST_URI}  !^/request.php
    RewriteCond     %{REQUEST_URI}  !^/robots.txt$
    RewriteRule     (.*)            /index.php$1       [L,QSA]

    php_admin_value   max_execution_time    600
    php_admin_value   max_input_time        600
    php_admin_value   max_input_vars        60000
    php_admin_value   upload_max_filesize   "400M"
    php_admin_value   post_max_size         "400M"

    # Ajout d'une directive pour éviter les conflits
    SetEnvIf Host "^api.localhost$" API_ENV

    ErrorLog ${APACHE_LOG_DIR}/api_error.log
    CustomLog ${APACHE_LOG_DIR}/api_access.log combined
</VirtualHost>
EOF

# VirtualHost pour SYNC (dans l'engine)
cat > docker/apache/sites-available/sync.conf << 'EOF'
<VirtualHost *:80>
    ServerName sync.localhost
    ServerAlias www.sync.localhost
    DocumentRoot /var/www/engine/htdocs/sync/

    <Directory /var/www/engine/htdocs/sync/>
        Options -Indexes +FollowSymLinks -MultiViews
        AllowOverride All
        Require all granted
    </Directory>

    php_flag display_startup_errors off
    php_flag display_errors on
    php_value error_reporting 22519

    php_admin_value   include_path          "/var/www/engine/include/"

    ## Specific riashop
    AcceptPathInfo  On
    RewriteEngine   On

    RewriteCond     %{REQUEST_URI}  !^/request.php
    RewriteCond     %{REQUEST_URI}  !^/robots.txt$
    RewriteRule     (.*)            /index.php$1       [L,QSA]

    php_admin_value   max_execution_time    600
    php_admin_value   max_input_time        600
    php_admin_value   max_input_vars        60000
    php_admin_value   upload_max_filesize   "400M"
    php_admin_value   post_max_size         "400M"

    # Ajout d'une directive pour éviter les conflits
    SetEnvIf Host "^sync.localhost$" SYNC_ENV

    ErrorLog ${APACHE_LOG_DIR}/sync_error.log
    CustomLog ${APACHE_LOG_DIR}/sync_access.log combined
</VirtualHost>
EOF

# VirtualHost pour le backoffice Riashop
cat > docker/apache/sites-available/backoffice.conf << 'EOF'
<VirtualHost *:80>
    ServerName localhost
    ServerAlias www.localhost
    DocumentRoot /var/www/sites/riashop/htdocs/

    <Directory /var/www/sites/riashop/htdocs/>
        Options -Indexes +FollowSymLinks -MultiViews
        AllowOverride All
        Require all granted
        php_admin_value include_path "/var/www/sites/riashop/include/:/var/www/engine/include/"
        php_value auto_prepend_file "/var/www/sites/riashop/htdocs/config.inc.php"
        php_admin_value session.auto_start 1
        php_admin_value default_charset "utf-8"
    </Directory>

    php_flag display_startup_errors off
    php_flag display_errors on
    php_value error_reporting 22519

    # Ajout d'une directive pour éviter les conflits
    SetEnvIf Host "^localhost$" BACKOFFICE_ENV

    ErrorLog ${APACHE_LOG_DIR}/backoffice_error.log
    CustomLog ${APACHE_LOG_DIR}/backoffice_access.log combined
</VirtualHost>
EOF

# VirtualHost pour le frontend client (si configuré)
if [[ -n "$FRONTEND_SVN_URL" ]]; then
cat > docker/apache/sites-available/frontend.conf << EOF
<VirtualHost *:80>
    ServerName front.localhost
    ServerAlias www.front.localhost
    DocumentRoot /var/www/sites/$CLIENT_NAME/htdocs/

    <Directory /var/www/sites/$CLIENT_NAME/htdocs/>
        Options FollowSymlinks
        AllowOverride All
        Require all granted
    </Directory>

    AcceptPathInfo  On
    RewriteEngine   On
    php_value error_reporting 22519

    # Règles de réécriture pour les ressources statiques
    RewriteCond     %{REQUEST_URI}  !^/css.*
    RewriteCond     %{REQUEST_URI}  !^/images.*
    RewriteCond     %{REQUEST_URI}  !^/admin.*
    RewriteCond     %{REQUEST_URI}  !^/js.*
    RewriteCond     %{REQUEST_URI}  !^/video/.*
    RewriteCond     %{REQUEST_URI}  !^/favicon.ico$
    RewriteCond     %{REQUEST_URI}  !^/robots.txt$
    RewriteCond     %{REQUEST_URI}  !^/sync/
    RewriteCond     %{REQUEST_URI}  !^/catalogue-en-ligne/
    RewriteCond     %{REQUEST_URI}  !^/.well-known/
    Rewriterule     (.*)            /rewritemap.php$1       [L,QSA]

    php_admin_value  include_path        "/var/www/engine/include/:/var/www/sites/$CLIENT_NAME/htdocs/include/"
    php_admin_value  auto_prepend_file   "/var/www/sites/$CLIENT_NAME/htdocs/config.inc.php"
    php_admin_value  session.cookie_lifetime   604800
    php_admin_value  session.gc_maxlifetime    604800
    php_admin_value  session.auto_start        1

    ErrorLog \${APACHE_LOG_DIR}/frontend_error.log
    CustomLog \${APACHE_LOG_DIR}/frontend_access.log combined
</VirtualHost>
EOF
fi

# Configuration Supervisor
echo "⚙️ Configuration Supervisor..."
cat > docker/supervisor/supervisord.conf << 'EOF'
[supervisord]
nodaemon=true
user=root

[program:apache2]
command=/usr/sbin/apache2ctl -DFOREGROUND
stdout_logfile=/var/log/supervisor/apache2.log
stderr_logfile=/var/log/supervisor/apache2.log
autorestart=true

[program:cron]
command=/usr/sbin/cron -f
stdout_logfile=/var/log/supervisor/cron.log
stderr_logfile=/var/log/supervisor/cron.log
autorestart=true
EOF

# Script d'initialisation du conteneur principal
echo "🔧 Création du script d'initialisation..."
cat > docker/scripts/init.sh << 'EOF'
#!/bin/bash

# Activation des sites Apache
a2ensite api
a2ensite sync
a2ensite backoffice
if [ -f /etc/apache2/sites-available/frontend.conf ]; then
    a2ensite frontend
fi

# Désactivation du site par défaut
a2dissite 000-default

cat > docker/scripts/init.sh << 'EOF'
#!/bin/bash

# Activation des sites Apache
a2ensite api
a2ensite sync
a2ensite backoffice
if [ -f /etc/apache2/sites-available/frontend.conf ]; then
    a2ensite frontend
fi

# Désactivation du site par défaut
a2dissite 000-default

# Gestion du fichier env.inc.php
if [ -d /var/www/engine/include ]; then
    if [ ! -f /var/www/engine/include/env.inc.php ] && [ -f /etc/riashop/env.inc.php.template ]; then
        echo "Création du fichier env.inc.php par défaut..."
        cp /etc/riashop/env.inc.php.template /var/www/engine/include/env.inc.php
    elif [ -f /etc/riashop/env.inc.php.template ]; then
        echo "Le fichier env.inc.php existe, création de env.local.inc.php..."
        cp /etc/riashop/env.inc.php.template /var/www/engine/include/env.local.inc.php
        # Vérifier si env.local.inc.php est déjà inclus dans env.inc.php
        if ! grep -q "env.local.inc.php" /var/www/engine/include/env.inc.php; then
            echo "Ajout de l'inclusion de env.local.inc.php..."
            echo "" >> /var/www/engine/include/env.inc.php
            echo "// Configuration locale Docker" >> /var/www/engine/include/env.inc.php
            echo "if (file_exists(dirname(__FILE__) . '/env.local.inc.php')) {" >> /var/www/engine/include/env.inc.php
            echo "    include_once dirname(__FILE__) . '/env.local.inc.php';" >> /var/www/engine/include/env.inc.php
            echo "}" >> /var/www/engine/include/env.inc.php
        fi
    fi
fi

# Configuration des crons si nécessaire
if [ -d /var/www/engine ]; then
    # Liens symboliques pour les crons
    if [ -f /var/www/engine/cron.daily ]; then
        ln -sf /var/www/engine/cron.daily /etc/cron.daily/riashop
    fi
    if [ -f /var/www/engine/cron.hourly ]; then
        ln -sf /var/www/engine/cron.hourly /etc/cron.hourly/riashop
    fi
    if [ -f /var/www/engine/cron.monthly ]; then
        ln -sf /var/www/engine/cron.monthly /etc/cron.monthly/riashop
    fi
    if [ -f /var/www/engine/cron.others ]; then
        ln -sf /var/www/engine/cron.others /etc/cron.d/riashop
    fi
fi

# Permissions
chown -R www-data:www-data /var/www
chmod -R 755 /var/www

echo "Initialisation terminée"
exec "$@"
EOF

# Configuration worker
echo "🤖 Configuration des workers..."
cat > docker/worker/config.json << 'EOF'
{
    "max_threads": 5,
    "beanstalk_host": "beanstalk",
    "beanstalk_port": 11300,
    "queues": {
        "low": "local-dev-low",
        "high": "local-dev-high",
        "higher": "local-dev-higher"
    },
    "php_executable": "/usr/bin/php5.6"
}
EOF

cat > docker/worker/start.sh << 'EOF'
#!/bin/bash
echo "Démarrage des workers Riashop..."
cd /opt/riashop-worker
python3 console.py --command=at
EOF

# Script SQL d'initialisation MariaDB
echo "🗄️ Configuration de la base de données..."
cat > docker/mariadb/init.sql << 'EOF'
-- Initialisation de la base Riashop
CREATE DATABASE IF NOT EXISTS riashop_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE riashop_db;

-- Tables de base (à adapter selon vos besoins)
CREATE TABLE IF NOT EXISTS ria_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(255) NOT NULL,
    config_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insertion de quelques valeurs par défaut
INSERT INTO ria_config (config_key, config_value) VALUES
('version', '1.0.0'),
('environment', 'local'),
('debug', 'true');
EOF

# Création du template .env avec les bonnes URLs
cat > config/.env.template << EOF
# Template de fichier .env pour l'environnement local Docker
# Configuration client: $CLIENT_NAME

# Identifiants client
ENVRIA_TNT_ID=1
ENVRIA_WST_ID=1

# Configuration Base de données MariaDB
ENVRIA_BDD_SERVER=mariadb
ENVRIA_BDD_LOGIN=riashop
ENVRIA_BDD_PWD=riashop123
ENVRIA_BDD_NAME=riashop_db

# Package Riashop
ENVRIA_PACKAGE=business

# Configuration CouchDB
ENVRIA_COUCHDB_PROTOCOL=http
ENVRIA_COUCHDB_URL=couchdb:5984
ENVRIA_COUCHDB_LOGIN=admin
ENVRIA_COUCHDB_PASSWORD=admin123

# Configuration Queue Beanstalk
ENVRIA_QUEUE_SERVER=beanstalk
ENVRIA_QUEUE_PREFIX=local-dev

# URLs API
ENVRIA_API_URL=http://api.localhost
ENVRIA_SYNC_URL=http://sync.localhost

# Répertoire Engine
ENVRIA_ENGINE_DIRECTORY=/var/www/engine/

# Configuration Memcache
ENVRIA_MEMCACHE_SERVER=memcache:11211

# Mode debug pour l'environnement local
ENVRIA_DEBUG=true
ENVRIA_ENV=local
EOF

# Copie du template .env dans le dossier engine si il n'existe pas
if [ ! -f www/engine/.env ] && [ -d www/engine ]; then
    cp config/.env.template www/engine/.env
fi

echo "🐳 Construction et démarrage des conteneurs..."
docker-compose build --no-cache
docker-compose up -d

echo ""
echo "✅ Installation terminée !"
echo ""
echo "📋 Configuration client: $CLIENT_NAME"
echo ""
echo "🌐 URLs disponibles :"
echo "   - Backoffice Riashop: http://localhost"
echo "   - API (engine): http://api.localhost"
echo "   - Sync (engine): http://sync.localhost"
if [[ -n "$FRONTEND_SVN_URL" ]]; then
echo "   - Frontend client: http://front.localhost"
fi
echo "   - PhpMyAdmin: http://localhost:8080"
echo "   - CouchDB: http://localhost:5984"
echo ""
echo "📁 Structure des dossiers :"
echo "   - Engine: www/engine/ (accès local uniquement)"
echo "   - Backoffice: www/sites/riashop/"
echo "   - Media: www/media/"
if [[ -n "$FRONTEND_SVN_URL" ]]; then
echo "   - Frontend: www/sites/$CLIENT_NAME/"
fi
echo ""
echo "🔧 Base de données :"
echo "   - Host: localhost:3306"
echo "   - User: riashop"
echo "   - Password: riashop123"
echo "   - Database: riashop_db"
echo ""
echo "🏠 Ajoutez ces entrées à votre fichier /etc/hosts :"
echo "127.0.0.1 localhost api.localhost sync.localhost front.localhost"
echo ""
echo "📝 Commandes utiles :"
echo "   - Voir les logs : docker-compose logs -f"
echo "   - Arrêter : docker-compose down"
echo "   - Mettre à jour les sources : $0 --engine \"$ENGINE_SVN_URL\" --backoffice \"$BACKOFFICE_SVN_URL\" --frontend \"$FRONTEND_SVN_URL\" --client \"$CLIENT_NAME\""