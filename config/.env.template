# Template de fichier .env pour l'environnement local Docker
# Configuration client: Sardeco

# Identifiants client
ENVRIA_TNT_ID=1
ENVRIA_WST_ID=1

# Configuration Base de données MariaDB
ENVRIA_BDD_SERVER=mariadb
ENVRIA_BDD_LOGIN=riashop
ENVRIA_BDD_PWD=riashop123
ENVRIA_BDD_NAME=riashop_db

# Package Riashop
ENVRIA_PACKAGE=business

# Configuration CouchDB
ENVRIA_COUCHDB_PROTOCOL=http
ENVRIA_COUCHDB_URL=couchdb:5984
ENVRIA_COUCHDB_LOGIN=admin
ENVRIA_COUCHDB_PASSWORD=admin123

# Configuration Queue Beanstalk
ENVRIA_QUEUE_SERVER=beanstalk
ENVRIA_QUEUE_PREFIX=local-dev

# URLs API
ENVRIA_API_URL=http://api.localhost
ENVRIA_SYNC_URL=http://sync.localhost

# Répertoire Engine
ENVRIA_ENGINE_DIRECTORY=/var/www/engine/

# Configuration Memcache
ENVRIA_MEMCACHE_SERVER=memcache:11211

# Mode debug pour l'environnement local
ENVRIA_DEBUG=true
ENVRIA_ENV=local
