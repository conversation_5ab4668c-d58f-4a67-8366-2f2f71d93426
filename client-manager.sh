#!/bin/bash

# Gestionnaire de clients Riashop - Switch entre environnements clients
set -e

CLIENTS_CONFIG_DIR="./clients"
CURRENT_CLIENT_FILE="./.current-client"
BACKUP_DIR="./backups/clients"

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction d'aide
show_help() {
    cat << EOF
Gestionnaire de clients Riashop

Usage: $0 [COMMAND] [OPTIONS]

Commands:
    list                           Liste tous les clients configurés
    add [CLIENT_NAME]              Ajoute un nouveau client (mode interactif si pas de nom)
    switch CLIENT_NAME             Switch vers un client
    current                        Affiche le client actuel
    backup CLIENT_NAME             Sauvegarde la config d'un client
    restore CLIENT_NAME            Restaure la config d'un client
    remove CLIENT_NAME             Supprime un client
    status                         État de tous les clients

Options pour 'add':
    --tnt-id ID                    ID tenant (obligatoire)
    --db-name NAME                 Nom de la base de données
    --db-host HOST                 Serveur BDD (défaut: mariadb)
    --db-user USER                 Utilisateur BDD (défaut: riashop)
    --db-pass PASS                 Mot de passe BDD
    --couchdb-prefix PREFIX        Préfixe CouchDB
    --queue-prefix PREFIX          Préfixe des queues
    --package TYPE                 Type de package (défaut: business)
    --frontend-svn URL             URL SVN Frontend (peut être utilisé plusieurs fois)
    --crons-enabled true/false     Activer les crons (défaut: false)
    --sql-file PATH                Fichier SQL à importer dans la BDD

Exemples:
    $0 add client1 --tnt-id 123 --db-name client1_db
    $0 add Sardeco --tnt-id 36 --sql-file "/path/to/sardeco.sql" \\
        --frontend-svn "https://svn.riashop.fr/svn/sardeco/branches/extranet" \\
        --frontend-svn "https://svn.riashop.fr/svn/sardeco/branches/main-site"
    $0 switch client1
    $0 backup client1
    $0 list
EOF
}

# Fonctions utilitaires
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Initialisation des dossiers
init_dirs() {
    mkdir -p "$CLIENTS_CONFIG_DIR"
    mkdir -p "$BACKUP_DIR"
}

# Obtenir le client actuel
get_current_client() {
    if [[ -f "$CURRENT_CLIENT_FILE" ]]; then
        cat "$CURRENT_CLIENT_FILE"
    else
        echo ""
    fi
}

# Vérifier si un client existe
client_exists() {
    local client_name="$1"
    [[ -f "$CLIENTS_CONFIG_DIR/$client_name.conf" ]]
}

# Lister les clients
list_clients() {
    log_info "Clients configurés:"
    echo

    local current_client=$(get_current_client)

    if [[ ! -d "$CLIENTS_CONFIG_DIR" ]] || [[ -z "$(ls -A "$CLIENTS_CONFIG_DIR" 2>/dev/null)" ]]; then
        log_warning "Aucun client configuré"
        return
    fi

    for config_file in "$CLIENTS_CONFIG_DIR"/*.conf; do
        if [[ -f "$config_file" ]]; then
            local client_name=$(basename "$config_file" .conf)
            local active_marker=""

            if [[ "$client_name" == "$current_client" ]]; then
                active_marker=" ${GREEN}[ACTUEL]${NC}"
            fi

            # Lecture des infos du client
            source "$config_file"
            echo -e "📋 ${BLUE}$client_name${NC}$active_marker"
            echo "   TNT_ID: $TNT_ID | DB: $DB_NAME"
            echo "   Crons: $([ "$CRONS_ENABLED" == "true" ] && echo "${GREEN}ON${NC}" || echo "${RED}OFF${NC}")"
            echo
        fi
    done
}

# Mode interactif pour ajouter un client
interactive_add_client() {
    echo
    echo "🚀 ${BLUE}Assistant de création de client Riashop${NC}"
    echo "========================================"
    echo

    # Nom du client
    local client_name=""
    while [[ -z "$client_name" ]]; do
        read -p "📝 Nom du client: " client_name
        if [[ -z "$client_name" ]]; then
            echo "❌ Le nom du client est obligatoire"
        elif [[ -f "$CLIENTS_CONFIG_DIR/$client_name.conf" ]]; then
            echo "❌ Le client '$client_name' existe déjà"
            client_name=""
        fi
    done

    # TNT_ID
    local tnt_id=""
    while [[ -z "$tnt_id" ]]; do
        read -p "🔢 TNT_ID (ID tenant): " tnt_id
        if [[ -z "$tnt_id" ]]; then
            echo "❌ Le TNT_ID est obligatoire"
        elif ! [[ "$tnt_id" =~ ^[0-9]+$ ]]; then
            echo "❌ Le TNT_ID doit être un nombre"
            tnt_id=""
        fi
    done

    # Base de données
    local db_name="${client_name}_db"
    read -p "🗄️  Nom de la base de données [$db_name]: " input_db_name
    if [[ -n "$input_db_name" ]]; then
        db_name="$input_db_name"
    fi

    # Fichier SQL
    local sql_file=""
    read -p "📄 Fichier SQL à importer (optionnel): " sql_file
    if [[ -n "$sql_file" && ! -f "$sql_file" ]]; then
        echo "⚠️  Fichier SQL introuvable: $sql_file"
        read -p "Continuer sans import de base de données? (y/N): " continue_without_sql
        if [[ "$continue_without_sql" != "y" && "$continue_without_sql" != "Y" ]]; then
            echo "❌ Création annulée"
            return 1
        fi
        sql_file=""
    fi

    # Frontend SVN URLs
    local frontend_svns=()
    echo
    echo "🌐 Configuration des frontends (optionnel)"
    echo "Appuyez sur Entrée pour terminer la saisie des frontends"
    local frontend_count=1
    while true; do
        read -p "Frontend SVN #$frontend_count (optionnel): " frontend_url
        if [[ -z "$frontend_url" ]]; then
            break
        fi
        frontend_svns+=("$frontend_url")
        ((frontend_count++))
    done

    # Résumé
    echo
    echo "📋 ${BLUE}Résumé de la configuration${NC}"
    echo "=========================="
    echo "Client: $client_name"
    echo "TNT_ID: $tnt_id"
    echo "Base de données: $db_name"
    echo "Fichier SQL: ${sql_file:-"Aucun"}"
    echo "Engine SVN: https://svn.riashop.fr/svn/riashop-engine/branches/localhost"
    echo "Backoffice SVN: https://svn.riashop.fr/svn/riashop-admin-transition/branches/localhost"
    if [[ ${#frontend_svns[@]} -gt 0 ]]; then
        echo "Frontend SVNs:"
        for i in "${!frontend_svns[@]}"; do
            echo "  $((i+1)). ${frontend_svns[i]}"
        done
    else
        echo "Frontend SVNs: Aucun"
    fi
    echo

    # Confirmation
    read -p "Créer ce client? (Y/n): " confirm
    if [[ "$confirm" == "n" || "$confirm" == "N" ]]; then
        echo "❌ Création annulée"
        return 1
    fi

    # Construction des arguments pour add_client
    local args=("$client_name" "--tnt-id" "$tnt_id" "--db-name" "$db_name")

    if [[ -n "$sql_file" ]]; then
        args+=("--sql-file" "$sql_file")
    fi

    for frontend_url in "${frontend_svns[@]}"; do
        args+=("--frontend-svn" "$frontend_url")
    done

    # Appel de la fonction add_client avec les arguments construits
    add_client "${args[@]}"
}

# Ajouter un nouveau client
add_client() {
    local client_name="$1"
    shift

    if [[ -z "$client_name" ]]; then
        log_error "Nom du client requis"
        show_help
        exit 1
    fi

    if client_exists "$client_name"; then
        log_error "Le client '$client_name' existe déjà"
        exit 1
    fi

    # Valeurs par défaut
    local TNT_ID=""
    local DB_NAME="${client_name}_db"
    local DB_HOST="mariadb"
    local DB_USER="riashop"
    local DB_PASS="riashop123"
    local COUCHDB_PREFIX="${client_name}_"
    local QUEUE_PREFIX="$client_name"
    local PACKAGE="business"
    local ENGINE_SVN="https://svn.riashop.fr/svn/riashop-engine/branches/localhost"
    local BACKOFFICE_SVN="https://svn.riashop.fr/svn/riashop-admin-transition/branches/localhost"
    local FRONTEND_SVNS=()  # Array pour supporter plusieurs frontends
    local CRONS_ENABLED="false"
    local SQL_FILE=""

    # Parse des arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --tnt-id)
                TNT_ID="$2"
                shift 2
                ;;
            --db-name)
                DB_NAME="$2"
                shift 2
                ;;
            --db-host)
                DB_HOST="$2"
                shift 2
                ;;
            --db-user)
                DB_USER="$2"
                shift 2
                ;;
            --db-pass)
                DB_PASS="$2"
                shift 2
                ;;
            --couchdb-prefix)
                COUCHDB_PREFIX="$2"
                shift 2
                ;;
            --queue-prefix)
                QUEUE_PREFIX="$2"
                shift 2
                ;;
            --package)
                PACKAGE="$2"
                shift 2
                ;;
            --frontend-svn)
                FRONTEND_SVNS+=("$2")
                shift 2
                ;;
            --crons-enabled)
                CRONS_ENABLED="$2"
                shift 2
                ;;
            --sql-file)
                SQL_FILE="$2"
                shift 2
                ;;
            *)
                log_error "Option inconnue: $1"
                exit 1
                ;;
        esac
    done

    if [[ -z "$TNT_ID" ]]; then
        log_error "TNT_ID est obligatoire"
        exit 1
    fi

    # Création du fichier de configuration client
    cat > "$CLIENTS_CONFIG_DIR/$client_name.conf" << EOF
# Configuration client: $client_name
# Créé le: $(date)

CLIENT_NAME="$client_name"
TNT_ID="$TNT_ID"
DB_NAME="$DB_NAME"
DB_HOST="$DB_HOST"
DB_USER="$DB_USER"
DB_PASS="$DB_PASS"
COUCHDB_PREFIX="$COUCHDB_PREFIX"
QUEUE_PREFIX="$QUEUE_PREFIX"
PACKAGE="$PACKAGE"
ENGINE_SVN="$ENGINE_SVN"
BACKOFFICE_SVN="$BACKOFFICE_SVN"
FRONTEND_SVNS=($(printf '"%s" ' "${FRONTEND_SVNS[@]}"))
CRONS_ENABLED="$CRONS_ENABLED"

# Génération d'un identifiant unique pour éviter les conflits de BDD
DB_UNIQUE_ID="$(date +%s)_$client_name"
EOF

    log_success "Client '$client_name' ajouté avec succès"
    log_info "Fichier de configuration: $CLIENTS_CONFIG_DIR/$client_name.conf"

    # Import de la base de données si un fichier SQL est fourni
    if [[ -n "$SQL_FILE" ]]; then
        log_info "Import de la base de données en cours..."

        # Validation du fichier SQL
        if [[ ! -f "$SQL_FILE" ]]; then
            log_error "Fichier SQL introuvable: $SQL_FILE"
            log_warning "Client créé mais sans base de données importée"
            return 1
        fi

        # Construction du nom de base de données unique
        local unique_db_name="${DB_NAME}_$(date +%s)_${client_name}"

        # Appel du script d'import de base de données
        if [[ -f "./scripts/database-import.sh" ]]; then
            log_info "Lancement de l'import de base de données..."
            if ./scripts/database-import.sh "$SQL_FILE" "$unique_db_name" "$client_name"; then
                log_success "Base de données importée avec succès!"

                # Mise à jour du fichier de configuration avec le nom de BDD unique
                sed -i.bak "s/DB_NAME=\"$DB_NAME\"/DB_NAME=\"$unique_db_name\"/" "$CLIENTS_CONFIG_DIR/$client_name.conf"
                rm -f "$CLIENTS_CONFIG_DIR/$client_name.conf.bak"

                log_info "Configuration mise à jour avec le nom de BDD: $unique_db_name"
            else
                log_error "Échec de l'import de la base de données"
                log_warning "Client créé mais l'import de la BDD a échoué"
                return 1
            fi
        else
            log_error "Script d'import de base de données introuvable: ./scripts/database-import.sh"
            log_warning "Client créé mais sans import de BDD"
            return 1
        fi
    fi
}

# Sauvegarder l'état actuel d'un client
backup_current_state() {
    local client_name="$1"
    local backup_timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_file="$BACKUP_DIR/${client_name}_${backup_timestamp}.tar.gz"

    log_info "Sauvegarde de l'état actuel..."

    # Création du backup
    mkdir -p "$BACKUP_DIR"

    # Sauvegarde de la BDD si elle existe
    if docker-compose ps mariadb | grep -q "Up"; then
        log_info "Sauvegarde de la base de données..."
        docker-compose exec -T mariadb mysqldump -u riashop -priashop123 --all-databases > "$BACKUP_DIR/${client_name}_${backup_timestamp}_db.sql"
    fi

    # Sauvegarde des fichiers de config
    tar -czf "$backup_file" \
        -C www engine/include/env.inc.php 2>/dev/null || true

    log_success "Sauvegarde créée: $backup_file"
}

# Switcher vers un client
switch_client() {
    local client_name="$1"

    if [[ -z "$client_name" ]]; then
        log_error "Nom du client requis"
        exit 1
    fi

    if ! client_exists "$client_name"; then
        log_error "Le client '$client_name' n'existe pas"
        log_info "Utilisez '$0 list' pour voir les clients disponibles"
        exit 1
    fi

    local current_client=$(get_current_client)

    # Sauvegarde de l'état actuel si un client était actif
    if [[ -n "$current_client" ]] && [[ "$current_client" != "$client_name" ]]; then
        log_info "Sauvegarde de l'état actuel du client '$current_client'"
        backup_current_state "$current_client"
    fi

    # Chargement de la configuration du nouveau client
    source "$CLIENTS_CONFIG_DIR/$client_name.conf"

    log_info "Switch vers le client: $client_name"

    # 1. Création/modification de env.inc.php
    create_env_config "$client_name"

    # 2. Gestion de la base de données
    setup_database "$client_name"

    # 3. Gestion des crons
    setup_crons "$client_name"

    # 4. Setup des sources SVN si spécifiées
    setup_svn_sources "$client_name"

    # 5. Redémarrage des services
    restart_services

    # Enregistrement du client actuel
    echo "$client_name" > "$CURRENT_CLIENT_FILE"

    log_success "Switch vers '$client_name' terminé avec succès!"
    show_client_info "$client_name"
}

# Création du fichier env.inc.php
create_env_config() {
    local client_name="$1"

    log_info "Configuration de env.inc.php pour '$client_name'"

    # Création du fichier env.local.inc.php pour ce client
    cat > "www/engine/include/env.local.inc.php" << EOF
<?php
// Configuration client: $client_name
// Généré automatiquement le: $(date)
// ⚠️  CRONS DÉSACTIVÉS PAR DÉFAUT POUR ÉVITER IMPACT PRODUCTION

\$env = [
    // Identifiants client
    'ENVRIA_TNT_ID' => '$TNT_ID',

    // Configuration Base de données
    'ENVRIA_BDD_SERVER' => '$DB_HOST',
    'ENVRIA_BDD_LOGIN' => '$DB_USER',
    'ENVRIA_BDD_PWD' => '$DB_PASS',
    'ENVRIA_BDD_NAME' => '${DB_NAME}_${DB_UNIQUE_ID}', // Nom unique pour éviter conflits

    // Package Riashop
    'ENVRIA_PACKAGE' => '$PACKAGE',

    // Configuration CouchDB
    'ENVRIA_COUCHDB_PROTOCOL' => 'http',
    'ENVRIA_COUCHDB_URL' => 'couchdb:5984',
    'ENVRIA_COUCHDB_LOGIN' => 'admin',
    'ENVRIA_COUCHDB_PASSWORD' => 'admin123',
    'ENVRIA_COUCHDB_DB_NAME_PREFIX' => '${COUCHDB_PREFIX}local_',

    // Configuration Queue Beanstalk
    'ENVRIA_QUEUE_SERVER' => 'beanstalk:11300',
    'ENVRIA_QUEUE_PREFIX' => '${QUEUE_PREFIX}-local',

    // URLs API
    'ENVRIA_API_URL' => 'http://api.localhost',
    'ENVRIA_SYNC_URL' => 'http://sync.localhost',

    // Répertoire Engine
    'ENVRIA_ENGINE_DIRECTORY' => dirname(__FILE__) . '/../',

    // Configuration locale
    'ENVRIA_DEBUG' => 'true',
    'ENVRIA_ENV' => 'local',
    'ENVRIA_CLIENT_NAME' => '$client_name',

    // 🚫 CRONS DÉSACTIVÉS PAR DÉFAUT
    'ENVRIA_CRONS_ENABLED' => '$CRONS_ENABLED',
];

// Application des variables d'environnement
foreach (\$env as \$key => \$value) {
    putenv("\$key=\$value");
}

// Log du client actuel
error_log("Riashop Client Local: $client_name (TNT_ID: $TNT_ID)");
EOF

    # Vérification que env.inc.php inclut le fichier local
    if [[ -f "www/engine/include/env.inc.php" ]]; then
        if ! grep -q "env.local.inc.php" "www/engine/include/env.inc.php"; then
            log_info "Ajout de l'inclusion du fichier local dans env.inc.php"
            cat >> "www/engine/include/env.inc.php" << 'EOF'

// Configuration locale Docker (ajoutée automatiquement)
if (file_exists(dirname(__FILE__) . '/env.local.inc.php')) {
    include_once dirname(__FILE__) . '/env.local.inc.php';
}
EOF
        fi
    fi
}

# Setup de la base de données avec nom unique
setup_database() {
    local client_name="$1"

    local unique_db_name="${DB_NAME}_${DB_UNIQUE_ID}"

    log_info "Configuration de la base de données: $unique_db_name"

    # Création de la base avec nom unique pour éviter les conflits
    if docker-compose ps mariadb | grep -q "Up"; then
        log_info "Création de la base de données '$unique_db_name'"
        docker-compose exec -T mariadb mysql -u root -proot123 << EOF
CREATE DATABASE IF NOT EXISTS \`$unique_db_name\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
GRANT ALL PRIVILEGES ON \`$unique_db_name\`.* TO '$DB_USER'@'%';
FLUSH PRIVILEGES;
EOF
        log_success "Base de données '$unique_db_name' créée"
    else
        log_warning "MariaDB n'est pas démarré, la base sera créée au prochain démarrage"
    fi
}

# Setup des crons (DÉSACTIVÉS par défaut)
setup_crons() {
    local client_name="$1"

    log_info "Configuration des crons (état: $CRONS_ENABLED)"

    # Suppression des liens existants
    docker-compose exec riashop-main bash -c "
        rm -f /etc/cron.daily/riashop-* 2>/dev/null || true
        rm -f /etc/cron.hourly/riashop-* 2>/dev/null || true
        rm -f /etc/cron.monthly/riashop-* 2>/dev/null || true
        rm -f /etc/cron.d/riashop-* 2>/dev/null || true
    " 2>/dev/null || true

    if [[ "$CRONS_ENABLED" == "true" ]]; then
        log_warning "⚠️  Activation des crons pour '$client_name'"
        log_warning "⚠️  Assurez-vous que cela n'impacte pas la production!"

        # Création des liens symboliques pour ce client
        docker-compose exec riashop-main bash -c "
            if [[ -f /var/www/engine/cron.daily ]]; then
                ln -sf /var/www/engine/cron.daily /etc/cron.daily/riashop-$client_name
            fi
            if [[ -f /var/www/engine/cron.hourly ]]; then
                ln -sf /var/www/engine/cron.hourly /etc/cron.hourly/riashop-$client_name
            fi
            if [[ -f /var/www/engine/cron.monthly ]]; then
                ln -sf /var/www/engine/cron.monthly /etc/cron.monthly/riashop-$client_name
            fi
        " 2>/dev/null || log_warning "Impossible de configurer les crons (conteneur non démarré?)"
    else
        log_success "🚫 Crons DÉSACTIVÉS (sécurisé pour éviter impact production)"
    fi
}

# Setup des sources SVN
setup_svn_sources() {
    local client_name="$1"

    log_info "Mise à jour des sources SVN pour '$client_name'"

    # Engine SVN (toujours configuré)
    log_info "Engine SVN: $ENGINE_SVN"
    if [[ -d "www/engine/.svn" ]]; then
        cd www/engine && svn switch "$ENGINE_SVN" && cd ../..
    else
        svn checkout "$ENGINE_SVN" www/engine
    fi

    # Backoffice SVN (toujours configuré)
    log_info "Backoffice SVN: $BACKOFFICE_SVN"
    if [[ -d "www/sites/riashop/.svn" ]]; then
        cd www/sites/riashop && svn switch "$BACKOFFICE_SVN" && cd ../../..
    else
        svn checkout "$BACKOFFICE_SVN" www/sites/riashop
    fi

    # Frontend SVNs (optionnel, peut être multiple)
    if [[ ${#FRONTEND_SVNS[@]} -gt 0 ]]; then
        log_info "Configuration de ${#FRONTEND_SVNS[@]} frontend(s) SVN"

        local index=0
        for frontend_url in "${FRONTEND_SVNS[@]}"; do
            local frontend_dir="www/sites/${client_name}"
            if [[ ${#FRONTEND_SVNS[@]} -gt 1 ]]; then
                frontend_dir="www/sites/${client_name}_frontend_$((index + 1))"
            fi

            log_info "Frontend SVN $((index + 1)): $frontend_url -> $frontend_dir"
            if [[ -d "$frontend_dir/.svn" ]]; then
                cd "$frontend_dir" && svn switch "$frontend_url" && cd - >/dev/null
            else
                svn checkout "$frontend_url" "$frontend_dir"
            fi
            ((index++))
        done
    else
        log_info "Aucune URL Frontend SVN fournie, utilisation du backoffice uniquement"
    fi
}

# Redémarrage des services
restart_services() {
    log_info "Redémarrage des services Docker..."
    docker-compose restart riashop-main riashop-worker 2>/dev/null || log_warning "Services non démarrés"
}

# Affichage des infos du client
show_client_info() {
    local client_name="$1"
    source "$CLIENTS_CONFIG_DIR/$client_name.conf"

    echo
    log_success "Client actuel: $client_name"
    echo "📋 Informations:"
    echo "   TNT_ID: $TNT_ID"
    echo "   Base de données: ${DB_NAME}_${DB_UNIQUE_ID}"
    echo "   Crons: $([ "$CRONS_ENABLED" == "true" ] && echo "${GREEN}ACTIVÉS${NC}" || echo "${RED}DÉSACTIVÉS${NC}")"
    echo
    echo "🌐 URLs disponibles:"
    echo "   - Backoffice: http://localhost"
    echo "   - API: http://api.localhost"
    echo "   - Sync: http://sync.localhost"
    if [[ -n "$FRONTEND_SVN" ]]; then
        echo "   - Frontend: http://front.localhost"
    fi
}

# Affichage du client actuel
show_current() {
    local current_client=$(get_current_client)
    if [[ -n "$current_client" ]]; then
        show_client_info "$current_client"
    else
        log_warning "Aucun client actuel"
    fi
}

# Suppression d'un client
remove_client() {
    local client_name="$1"

    if [[ -z "$client_name" ]]; then
        log_error "Nom du client requis"
        exit 1
    fi

    if ! client_exists "$client_name"; then
        log_error "Le client '$client_name' n'existe pas"
        exit 1
    fi

    read -p "Êtes-vous sûr de vouloir supprimer le client '$client_name'? (y/N) " -n 1 -r
    echo

    if [[ $REPLY =~ ^[Yy]$ ]]; then
        rm "$CLIENTS_CONFIG_DIR/$client_name.conf"

        # Si c'est le client actuel, on le désactive
        local current_client=$(get_current_client)
        if [[ "$current_client" == "$client_name" ]]; then
            rm -f "$CURRENT_CLIENT_FILE"
        fi

        log_success "Client '$client_name' supprimé"
    else
        log_info "Suppression annulée"
    fi
}

# Main
init_dirs

case "${1:-}" in
    list)
        list_clients
        ;;
    add)
        if [[ -z "$2" ]]; then
            # Mode interactif si aucun nom de client fourni
            interactive_add_client
        else
            # Mode traditionnel avec paramètres
            add_client "${@:2}"
        fi
        ;;
    switch)
        switch_client "$2"
        ;;
    current)
        show_current
        ;;
    remove)
        remove_client "$2"
        ;;
    backup)
        backup_current_state "$2"
        ;;
    status)
        list_clients
        echo
        show_current
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo "Commande inconnue: ${1:-}"
        echo
        show_help
        exit 1
        ;;
esac